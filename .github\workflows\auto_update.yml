name: 'Auto Update'
 
on:
  schedule:
  - cron: "23 0 * * *"
  workflow_dispatch:
 
jobs:
  sync:
    name: 'Auto Update'
    runs-on: ubuntu-latest
 
    defaults:
      run:
        shell: bash
 
    steps:
    - name: Checkout
      uses: actions/checkout@v2
      with:
        token: ${{ secrets.MY_SECRETS_TOKEN }}
        submodules: true

    - name: Git Submodule Update
      run: |
        git submodule init
        git submodule update --remote
        
    - name: uugamebooster auto update
      run: |
        cd packages/net/uugamebooster
        ./update.sh

    - name: fakehttp auto update
      run: |
        cd packages/net/fakehttp
        ./update.sh

    - name: Commit update
      run: |
        git config --global user.name 'github-actions[bot]'
        git config --global user.email 'github-actions[bot]@users.noreply.github.com'
        git remote set-url origin https://x-access-token:${{ secrets.GITHUB_TOKEN }}@github.com/${{ github.repository }}
        
        UU_CHANGES=""
        if [ -n "$(git status packages/net/uugamebooster/Makefile --porcelain)" ]; then
          UU_CHANGES="uugamebooster to $(awk -F "PKG_VERSION:=" '{print $2}' "packages/net/uugamebooster/Makefile" | xargs)"
        fi
        
        SUBMODULE_CHANGES=""
        if [ -n "$(git status luci/applications/luci-app-easytier --porcelain)" ]; then
          SUBMODULE_CHANGES="Submodule luci-app-easytier to $(git submodule status luci/applications/luci-app-easytier | awk '{print $1}')"
        fi
        
        FAKEHTTP_CHANGES=""
        if [ -n "$(git status packages/net/fakehttp/Makefile --porcelain)" ]; then
          FAKEHTTP_CHANGES="fakehttp to $(awk -F "PKG_VERSION:=" '{print $2}' "packages/net/fakehttp/Makefile" | xargs)"
        fi
        
        if [ -n "$UU_CHANGES" ] || [ -n "$SUBMODULE_CHANGES" ] || [ -n "$FAKEHTTP_CHANGES" ]; then
          COMMIT_MSG="Update: "
          if [ -n "$UU_CHANGES" ]; then
            COMMIT_MSG+="$UU_CHANGES"
            git add packages/net/uugamebooster/Makefile
          fi
          if [ -n "$SUBMODULE_CHANGES" ]; then
            [ -n "$UU_CHANGES" ] && COMMIT_MSG+=" and "
            COMMIT_MSG+="$SUBMODULE_CHANGES"
            git add .gitmodules luci/applications/luci-app-easytier
          fi
          if [ -n "$FAKEHTTP_CHANGES" ]; then
            [ -n "$UU_CHANGES" ] && COMMIT_MSG+=" and "
            [ -n "$SUBMODULE_CHANGES" ] && COMMIT_MSG+=" and "
            COMMIT_MSG+="$FAKEHTTP_CHANGES"
            git add packages/net/fakehttp/Makefile
          fi
          git commit -m "$COMMIT_MSG"
          git push
        fi

    - name: Delete workflow runs
      uses: Mattraks/delete-workflow-runs@main
      with:
        token: ${{ github.token }}
        retain_days: 1
        keep_minimum_runs: 3
