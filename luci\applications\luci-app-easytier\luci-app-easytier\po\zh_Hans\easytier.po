#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:648
#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:666
msgid ""
"- Program /tmp/easytier-cli uploaded successfully, restart the plugin to "
"take effect"
msgstr "- 程序/tmp/easytier-cli上传成功，重启一次插件才生效"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:640
#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:658
msgid ""
"- Program /tmp/easytier-core uploaded successfully, restart the plugin to "
"take effect"
msgstr "- 程序/tmp/easytier-core上传成功，重启一次插件才生效"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:654
#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:672
msgid ""
"- Program /tmp/easytier-web uploaded successfully, restart the plugin to "
"take effect"
msgstr "- 程序/tmp/easytier-web上传成功，重启一次插件才生效"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:5
msgid ""
"A simple, secure, decentralized VPN solution for intranet penetration, "
"implemented in Rust using the Tokio framework. Project URL: <a href="
"\"https://github.com/EasyTier/EasyTier\" target=\"_blank\">github.com/"
"EasyTier/EasyTier</a>&nbsp;&nbsp;<a href=\"http://easytier.cn\" target="
"\"_blank\">Official Documentation</a>&nbsp;&nbsp;<a href=\"http://qm.qq.com/"
"cgi-bin/qm/qr?"
"_wv=1027&k=jhP2Z4UsEZ8wvfGPLrs0VwLKn_uz0Q_p&authKey=OGKSQLfg61YPCpVQuvx"
"%2BxE7hUKBVBEVi9PljrDKbHlle6xqOXx8sOwPPTncMambK&noverify=0&group_code=949700262\" "
"target=\"_blank\">QQ Group</a>&nbsp;&nbsp;<a href=\"https://doc.oee.icu"
"\" target=\"_blank\">Beginner Tutorial</a>"
msgstr ""
"一个简单、安全、去中心化的内网穿透 VPN 组网方案，使用 Rust 语言和 Tokio 框架实现。 "
"项目地址：<a href=\"https://github.com/EasyTier/EasyTier\" target=\"_blank\">github.com/EasyTier/EasyTier</a>&nbsp;&nbsp;"
"<a href=\"http://easytier.cn\" target=\"_blank\">官网文档</a>&nbsp;&nbsp;"
"<a href=\"http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=jhP2Z4UsEZ8wvfGPLrs0VwLKn_uz0Q_p&authKey=OGKSQLfg61YPCpVQuvx%2BxE7hUKBVBEVi9PljrDKbHlle6xqOXx8sOwPPTncMambK&noverify=0&group_code=949700262\" target=\"_blank\">QQ群</a>&nbsp;&nbsp;"
"<a href=\"https://doc.oee.icu\" target=\"_blank\">菜鸟教程</a>"
#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:720
msgid "API Port"
msgstr "API端口"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:381
msgid "Access Control"
msgstr "访问控制"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:18
msgid "Advanced Settings"
msgstr "高级设置"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:319
msgid "Allow Forwarding"
msgstr "允许转发"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:268
msgid "Allow this node to act as an exit node (--enable-exit-node parameter)"
msgstr "允许此节点成为出口节点 （--enable-exit-node 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:383
msgid "Allow traffic from EasyTier virtual network to LAN"
msgstr "允许从虚拟网络 EasyTier 到局域网 lan 的流量"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:384
msgid "Allow traffic from EasyTier virtual network to WAN"
msgstr "允许从虚拟网络 EasyTier 到广域网 wan 的流量"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:385
msgid "Allow traffic from LAN to EasyTier virtual network"
msgstr "允许从局域网 lan 到虚拟网络 EasyTier 的流量"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:386
msgid "Allow traffic from WAN to EasyTier virtual network"
msgstr "允许从广域网 wan 到虚拟网络 EasyTier 的流量"

#: applications/luci-app-easytier/luasrc/view/easytier/easytier_log.htm:30
#: applications/luci-app-easytier/luasrc/view/easytier/easytierweb_log.htm:30
msgid "Auto Refresh"
msgstr "自动刷新"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:325
msgid ""
"Bind the connector socket to the physical device to avoid routing issues."
"<br>For example, if the subnet proxy segment conflicts with a peer node, "
"binding the physical NIC enables normal communication. (--bind-device "
"parameter)"
msgstr ""
"将连接器的套接字绑定到物理设备以避免路由问题。<br>"
"比如子网代理网段与某节点的网段冲突，绑定物理设备后可以与该节点正常通信。（ --bind-device 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:324
msgid "Bind to Physical NIC Only"
msgstr "仅使用物理网卡"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:394
msgid "Check IPs"
msgstr "检测IP"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:583
msgid "Check for Updates"
msgstr "检测更新"

#: applications/luci-app-easytier/luasrc/view/easytier/easytier_log.htm:31
#: applications/luci-app-easytier/luasrc/view/easytier/easytierweb_log.htm:31
msgid "Clear Logs"
msgstr "清除日志"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:469
msgid "Click the button to refresh and view STUN information"
msgstr "点击按钮刷新，执行 STUN 测试"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:546
msgid "Click the button to refresh and view TCP/KCP proxy information"
msgstr "点击按钮刷新，查看 TCP/KCP 代理状态"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:450
msgid "Click the button to refresh and view connector information"
msgstr "点击按钮刷新，查看管理连接器"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:412
msgid "Click the button to refresh and view local node information"
msgstr "点击按钮刷新，查看本机信息"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:431
msgid "Click the button to refresh and view peer information"
msgstr "点击按钮刷新，查看对等节点信息"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:508
msgid "Click the button to refresh and view peer-center information"
msgstr "点击按钮刷新，查看全局对等节点信息"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:489
msgid "Click the button to refresh and view route information"
msgstr "点击按钮刷新，查看路由信息"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:565
msgid ""
"Click the button to refresh and view the complete local startup parameters"
msgstr "点击按钮刷新，查看本机完整启动参数"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:527
msgid "Click the button to refresh and view vpn-portal information"
msgstr "点击按钮刷新，查看VPN 门户（WireGuard）信息"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:584
msgid ""
"Click the button to start checking for updates and refresh the version "
"display in the status bar above"
msgstr "点击按钮开始检测更新，刷新上方状态栏版本显示"

#: applications/luci-app-easytier/luasrc/view/easytier/easytier_status.htm:35
msgid "Collecting data..."
msgstr "收集数据..."

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:39
msgid "Default"
msgstr "默认"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:260
msgid "Compression Algorithm"
msgstr "压缩算法"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:261
msgid "Compression algorithm to use (--compression parameter)"
msgstr "使用的压缩算法 （--compression 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:40
#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:43
msgid "Configuration File"
msgstr "配置文件"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:715
msgid ""
"Configure the server's listening port for easytier-core to connect. (-c "
"parameter)"
msgstr "配置服务器的监听端口，用于被 easytier-core 连接。（ -c 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:721
msgid ""
"Listening port of the RESTful server, used as ApiHost by the web frontend. (-a parameter)"
msgstr "restful 服务器的监听端口，作为 ApiHost 并被 web 前端使用。（ -a 参数） "

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:708
msgid ""
"Configure the server's listening protocol for easytier-core to connect. (-p "
"parameter)"
msgstr "配置服务器的监听协议，用于被 easytier-core 连接。（ -p 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:19
msgid "Connection Info"
msgstr "连接信息"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:390
msgid "Connectivity Check"
msgstr "通断检测"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:449
msgid "Connector Info"
msgstr "connector信息"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:332
msgid ""
"Convert TCP traffic to KCP traffic to reduce latency and improve speed."
"<br>All nodes in the virtual network must be using EasyTier version v2.2.0 "
"or higher for this feature. (--enable-kcp-proxy parameter)"
msgstr "将TCP流量转为 KCP 流量，降低传输延迟，提升传输速度。<br>KCP 代理功能需要虚拟网内所有节点的 EasyTier 版本在 v2.2.0 以上。（ --enable-kcp-proxy 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:343
msgid ""
"Proxy tcp streams with QUIC, improving the latency and throughput on the network with udp packet loss."
"<br>All nodes in the virtual network must be using EasyTier version v2.3.2 or higher for this feature. "
"(--enable-quic-proxy parameter)"
msgstr "使用 QUIC 代理 TCP 流，提高在 UDP 丢包网络上的延迟和吞吐量。<br>QUIC 代理功能需要虚拟网内所有节点的 EasyTier 版本在 v2.3.2 以上。（ --enable-quic-proxy 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:236
msgid ""
"Custom name for the virtual TUN interface (--dev-name parameter)<br>If using "
"web configuration, please use the same virtual network interface name as in "
"the web config for firewall allowance"
msgstr "自定义虚拟网卡TUN接口的名称（--dev-name 参数）<br>如果是WEB配置请填写和WEB配置一样的虚拟网卡名称用于防火墙放行"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:581
msgid ""
"Customize the storage path for easytier-core. Make sure to provide the full "
"path and filename. If the specified path has insufficient space, it will "
"automatically move to /tmp/easytier-core"
msgstr "自定义easytier-core的存放路径，确保填写完整的路径及名称，若指定的路径可用空间不足将会自动移至/tmp/easytier-core"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:587
msgid ""
"Customize the storage path for easytier-web. Make sure to provide the full "
"path and filename, then upload the installer"
msgstr "自定义easytier-web的存放路径，确保填写完整的路径及名称，然后上传安装程序"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:703
msgid "Database File Path"
msgstr "数据库文件路径"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:367
#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:732
msgid "Debug"
msgstr "调试"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:733
msgid "Default API Server URL"
msgstr "默认API服务器URL"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:226
msgid "Default Protocol"
msgstr "默认协议"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:214
msgid ""
"Defines the URL of the VPN portal, allowing other VPN clients to connect."
"<br>Example: wg://0.0.0.0:11011/**********/24 means the VPN portal is a "
"WireGuard server listening on vpn.example.com:11010, and the VPN clients are "
"in the **********/24 network (--vpn-portal parameter)"
msgstr ""
"定义 VPN 门户的 URL，允许其他 VPN 客户端连接。<br> 示例：wg://0.0.0.0:11011/**********/24，"
"表示 VPN 门户是一个在 vpn.example.com:11010 上监听的 WireGuard 服务器，并且 VPN 客户端位于 **********/24 网络中（--vpn-portal 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:284
msgid "Disable Built-in NAT"
msgstr "禁用内置NAT"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:242
msgid "Disable Encryption"
msgstr "禁用加密"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:251
msgid "Disable IPv6"
msgstr "禁用ipv6"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:337
msgid "Disable KCP Input"
msgstr "禁用KCP输入"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:348
msgid "Disable QUIC Input"
msgstr "禁用QUIC输入"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:311
msgid "Disable P2P"
msgstr "禁用P2P"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:312
msgid ""
"Disable P2P communication; only use nodes specified by -p to forward packets "
"(--disable-p2p parameter)"
msgstr "禁用P2P通信；只通过-p指定的节点转发数据包 （ --disable-p2p 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:315
msgid "Disable UDP"
msgstr "禁用UDP"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:316
msgid "Disable UDP hole punching (--disable-udp-hole-punching parameter)"
msgstr "禁用UDP打洞功能（ --disable-udp-hole-punching 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:243
msgid ""
"Disable encryption for communication with peer nodes. If encryption is "
"disabled, all other nodes must also have encryption disabled (-u parameter)"
msgstr "禁用对等节点通信的加密，若关闭加密则其他节点也必须关闭加密 （-u 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:338
msgid ""
"Disallow other nodes from using KCP proxy TCP streams to this node.<br>KCP "
"proxy-enabled nodes accessing this node will still use the original method. "
"(--disable-kcp-input parameter)"
msgstr "不允许其他节点使用 KCP 代理 TCP 流到此节点。<br>开启 KCP 代理的节点访问此节点时，依然使用原始。（ --disable-kcp-input 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:349
msgid "Do not allow other nodes to use QUIC to proxy tcp streams to this node."
msgstr "不允许其他节点使用 QUIC 代理 TCP 流到此节点。"

msgid "When a node with QUIC proxy enabled accesses this node, the original tcp connection is preserved."
msgstr "开启 QUIC 代理的节点访问此节点时，依然使用原始 TCP 连接。"

msgid "<br>QUIC proxy-enabled nodes accessing this node will still use the original method. (--disable-quic-input parameter)"
msgstr "<br>启用 QUIC 代理的节点仍然会使用原始方式进行访问。（--disable-quic-input 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:139
msgid "Do Not Listen"
msgstr "不监听"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:281
msgid ""
"Do not create a TUN device; subnet proxying can still be used to access "
"nodes (--no-tun parameter)"
msgstr "不创建TUN设备，可以使用子网代理访问节点（ --no-tun 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:252
msgid "Do not use IPv6 (--disable-ipv6 parameter)"
msgstr "不使用ipv6 （--disable-ipv6 参数）"

#: applications/luci-app-easytier/luasrc/controller/easytier.lua:9
#: applications/luci-app-easytier/luasrc/controller/easytier.lua:10
msgid "EasyTier"
msgstr "EasyTier"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:14
msgid "EasyTier Configuration"
msgstr "EasyTier配置"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:22
#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:691
msgid "Enable"
msgstr "启用"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:89
msgid "Enable DHCP"
msgstr "启用DHCP"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:267
msgid "Enable Exit Node"
msgstr "启用出口节点"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:331
msgid "Enable KCP Proxy"
msgstr "启用KCP代理"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:342
msgid "Enable QUIC Proxy"
msgstr "启用QUIC代理"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:255
msgid "Enable Latency First"
msgstr "启用延迟优先"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:359
msgid "Enable Magic DNS"
msgstr "启用魔法DNS"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:247
msgid "Enable Multithreading"
msgstr "启用多线程"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:364
msgid "Enable Private Mode"
msgstr "启用私有模式"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:305
msgid ""
"Enable a SOCKS5 server to allow SOCKS5 clients to access the virtual "
"network. Leave blank to disable (--socks5 parameter)"
msgstr "启用 socks5 服务器，允许 socks5 客户端访问虚拟网络，留空则不开启（--socks5 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:391
msgid ""
"Enable connectivity check to specify remote device IPs; if all specified IPs "
"fail to ping, the EasyTier program will restart."
msgstr "开启通断检测后，可以指定对端的设备IP，当所有指定的IP都ping不通时将会重启easytier程序"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:248
msgid ""
"Enable multithreaded operation; single-threaded by default (--multi-thread "
"parameter)"
msgstr "使用多线程运行时，默认为单线程 （--multi-thread 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:277
msgid "Enable smoltcp stack for subnet proxying (--use-smoltcp parameter)"
msgstr "为子网代理启用smoltcp堆栈（--use-smoltcp 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:364
#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:729
msgid "Error"
msgstr "错误"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:626
msgid "Error: Upload failed!"
msgstr "错误：上传失败！"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:271
msgid "Exit Node Addresses"
msgstr "出口节点地址"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:272
msgid ""
"Exit nodes to forward all traffic through. These are virtual IPv4 addresses. "
"Priority is determined by the order in the list (--exit-nodes parameter)"
msgstr "转发所有流量的出口节点，虚拟 IPv4 地址，优先级由列表顺序确定（--exit-nodes 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:117
msgid ""
"Export the local network to other peers in the VPN, allowing access to other "
"devices in the current LAN (-n parameter)"
msgstr "将本地网络导出到 VPN 中的其他对等点，可访问当前局域网内其他设备 （-n 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:638
msgid "File has been uploaded to"
msgstr "文件已上传至"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:320
msgid ""
"Forward RPC packets from all peer nodes, even if they are not in the relay "
"network whitelist.<br>This can help peer nodes in non-whitelisted networks "
"establish P2P connections. (--relay-all-peer-rpc parameter)"
msgstr ""
"转发所有对等节点的RPC数据包，即使对等节点不在转发网络白名单中。<br>"
"这可以帮助白名单外网络中的对等节点建立P2P连接。（ -relay-all-peer-rpc 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:294
msgid "Forward Whitelisted Network Traffic"
msgstr "转发白名单网络的流量"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:354
msgid ""
"Forward a local port to a remote port within the virtual network."
"<br>Example: udp://0.0.0.0:12345/************:23456 means forwarding local "
"UDP port 12345 to ************:23456 in the virtual network.<br>Multiple "
"entries can be specified. (--port-forward parameter)"
msgstr ""
"将本地端口转发到虚拟网络中的远程端口。<br>例如：udp://0.0.0.0:12345/************:23456，"
"表示将本地UDP端口12345转发到虚拟网络中的************:23456。<br>可以指定多个。 （--port-forward 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:727
msgid ""
"Frontend listening port for the web dashboard server. Leave empty to "
"disable. (-l parameter)"
msgstr "web dashboard 服务器的前端监听端口，留空不启用。（ -l 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:17
msgid "General Settings"
msgstr "基本设置"

#: applications/luci-app-easytier/root/usr/share/rpcd/acl.d/luci-app-easytier.json:3
msgid "Grant UCI access for luci-app-easytier"
msgstr "授予UCI访问luci-app-easytier的权限"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:187
msgid "Hostname"
msgstr "主机名"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:90
msgid ""
"IP address will be automatically determined and assigned by EasyTier, "
"starting from ******** by default. Warning: When using DHCP, if an IP "
"conflict occurs in the network, the IP will be automatically changed. (-d "
"parameter)"
msgstr "由Easytier自动确定并设置IP地址，默认从********开始。警告：在使用DHCP时，如果网络中出现IP冲突，IP将自动更改。（-d 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:366
#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:731
msgid "Info"
msgstr "信息"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:102
msgid ""
"Initial connected peer nodes, same function as the parameter below (-p "
"parameter)<br>Public server status check: <a href='https://easytier.gd."
"nkbpal.cn/status/easytier' target='_blank'>Click here to check</a>"
msgstr ""
"初始连接的对等节点，和下方参数作用一样 （-p 参数）<br>公共服务器可用状态查询："
"<a href='https://easytier.gd.nkbpal.cn/status/easytier' target='_blank'>点此查询</a>"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:206
msgid "Instance Name"
msgstr "实例名称"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:94
msgid "Interface IP Address"
msgstr "接口IPV4地址"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:401
msgid "Interval Time (minutes)"
msgstr "间隔时间 (分钟)"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:402
msgid ""
"Interval time for checking connectivity; how often the specified IPs are "
"pinged."
msgstr "检测间隔的时间，每隔多久检测指定的IP通断一次"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:256
msgid ""
"Latency-first mode: attempts to forward traffic via the lowest latency path. "
"By default, the shortest path is used (--latency-first parameter)"
msgstr "延迟优先模式，将尝试使用最低延迟路径转发流量，默认使用最短路径 （--latency-first 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:138
msgid "Listen"
msgstr "监听"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:135
msgid "Listener Port"
msgstr "监听端口"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:707
msgid "Listening Protocol"
msgstr "监听协议"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:710
msgid ""
"Listening port of the RESTful server, used as ApiHost by the web frontend. (-"
"a parameter)"
msgstr "restful 服务器的监听端口，作为 ApiHost 并被 web 前端使用。（ -a 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:564
msgid "Local Startup Parameters"
msgstr "本机启动参数"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:220
msgid "MTU"
msgstr "MTU"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:221
msgid ""
"MTU for the TUN device, default is 1380 when unencrypted, and 1360 when "
"encrypted"
msgstr "TUN 设备的 MTU，默认值为非加密时的 1380，加密时为 1360"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:395
msgid ""
"Make sure the remote device IPs entered here are correct and reachable; "
"incorrect entries may cause ping failures and repeated program restarts."
msgstr "确保这里的对端设备IP地址填写正确且可访问，若填写错误将会导致无法ping通，程序反复重启"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:289
msgid ""
"Manually assign route CIDRs. This disables subnet proxying and WireGuard "
"routes propagated from peer nodes (--manual-routes parameter)"
msgstr "手动分配路由CIDR，将禁用子网代理和从对等节点传播的wireguard路由。（--manual-routes 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:121
msgid ""
"Manually specify the public IP address of this machine, so other nodes can "
"connect to this node using that address (domain names not supported).<br>For "
"example: tcp://***************:11223, multiple entries can be specified. (--"
"mapped-listeners parameter)"
msgstr ""
"手动指定本机的公网地址，其他节点可以使用该地址连接到本节点（不支持域名）。<br>例如：tcp://***************:11223，可以指定多个。"
"（--mapped-listeners 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:72
msgid "Network Name"
msgstr "网络名称"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:83
msgid "Network Secret"
msgstr "网络密钥"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:84
msgid ""
"Network secret used to verify whether this node belongs to the VPN network "
"(--network-secret parameter)"
msgstr "网络密钥，用于验证此节点是否属于 VPN 网络（--network-secret 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:280
msgid "No TUN Mode"
msgstr "无TUN模式"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:411
msgid "Node Info"
msgstr "node信息"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:136
msgid ""
"OFF: Do not listen on any port, only connect to peer nodes (--no-listener "
"parameter)<br>If used purely as a client (not as a server), you can choose "
"not to listen on a port"
msgstr "关闭:不监听任何端口，只连接到对等节点 （--no-listener 参数）<br>单纯作为客户端使用（不作为服务器）可以不监听端口"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:363
#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:728
msgid "Off"
msgstr "关闭"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:106
#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:113
msgid "Official Server - tcp://public.easytier.top:11010"
msgstr "官方服务器 - tcp://public.easytier.top:11010"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:35
msgid ""
"Official Web Console: <a href=\"https://easytier.cn/web\" target=\"_blank"
"\">https://easytier.cn/web</a><br>Official Configuration File Generator: <a "
"href=\"https://easytier.cn/web/index.html#/config_generator\" target=\"_blank"
"\">https://easytier.cn/web/index.html#/config_generator</a><br>Please note "
"to set the RPC port to 15888"
msgstr ""
"官方Web控制台：<a href='https://easytier.cn/web' target='_blank'>https://easytier.cn/web</a><br>"
"官方配置文件生成器：<a href='https://easytier.cn/web/index.html#/config_generator' "
"target='_blank'>https://easytier.cn/web/index.html#/config_generator</a><br>"
"注意配置RPC端口为15888"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:295
msgid ""
"Only forward traffic for whitelisted networks. By default, all networks are "
"allowed"
msgstr "仅转发白名单网络的流量，默认允许所有网络"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:299
msgid ""
"Only forward traffic for whitelisted networks. Input is a wildcard string, e."
"g., '*' (all networks), 'def*' (networks prefixed with 'def')<br>Multiple "
"networks can be specified. If empty, forwarding is disabled (--relay-network-"
"whitelist parameter)"
msgstr ""
"仅转发白名单网络的流量，输入是通配符字符串，例如：'*'（所有网络），'def*'（以def为前缀的网络）<br>"
"可以指定多个网络。如果参数为空，则禁用转发。（--relay-network-whitelist 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:704
msgid ""
"Path to the sqlite3 database file used to store all data. (-d parameter)"
msgstr " sqlite3 数据库文件路径, 用于保存所有数据。（ -d 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:430
msgid "Peer Info"
msgstr "Peer 信息"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:101
msgid "Peer Nodes"
msgstr "对等节点"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:507
msgid "Peer-Center Info"
msgstr "peer-center信息"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:353
msgid "Port Forwarding"
msgstr "端口转发"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:126
msgid "Portal Address Port"
msgstr "门户地址端口"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:369
#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:752
msgid "Program Log"
msgstr "程序日志"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:120
msgid "Public Addresses of Specified Listeners"
msgstr "指定监听器的公网地址"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:27
#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:696
msgid "Quickly restart once without modifying any parameters"
msgstr "在没有修改参数的情况下快速重新启动一次"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:127
msgid ""
"RPC portal address used for management. 0 means a random port, 12345 means "
"listening on port 12345 on localhost, 0.0.0.0:12345 means listening on port "
"12345 on all interfaces.<br>The default is 0; it is recommended to use 15888 "
"to avoid failure in obtaining status information (-r parameter)"
msgstr ""
"用于管理的 RPC 门户地址。0 表示随机端口，12345 表示监听本地主机的 12345 端口，0.0.0.0:12345 表示在所有接口上监听 12345 端口。<br>"
"默认值为 0，建议选 15888 防止无法获取状态信息 （-r 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:25
#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:26
#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:694
#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:695
msgid "Restart"
msgstr "重启"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:288
msgid "Route CIDR"
msgstr "路由CIDR"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:488
msgid "Route Info"
msgstr "Route信息"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:370
msgid ""
"Runtime log is located at /tmp/easytier.log. View it in the log section "
"above.<br>Levels: Error < Warning < Info < Debug < Trace"
msgstr ""
"运行日志在/tmp/easytier.log,可在上方日志查看<br>"
"详细程度：错误<警告<信息<调试<跟踪"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:753
msgid ""
"Runtime log located at /tmp/easytierweb.log, viewable in the log section "
"above.<br>Levels: Error < Warning < Info < Debug < Trace"
msgstr ""
"运行日志在/tmp/easytierweb.log,可在上方日志查看<br>"
"详细程度：错误<警告<信息<调试<跟踪"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:304
msgid "SOCKS5 Port"
msgstr "SOCKS5端口"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:468
msgid "STUN Info"
msgstr "STUN信息"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:687
msgid "Self-hosted Web Server"
msgstr "自建Web控制台"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:714
msgid "Server Port"
msgstr "服务端口"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:371
msgid "Set traffic permission rules between different network zones"
msgstr "设置不同网络区域之间的流量允许规则"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:109
msgid "Shared Node Address"
msgstr "共享节点地址"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:34
msgid "Startup Method"
msgstr "启动方式"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:116
msgid "Subnet Proxy"
msgstr "子网代理"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:700
msgid "TCP"
msgstr "TCP"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:545
msgid "TCP/KCP Proxy Info"
msgstr "TCP/KCP代理信息"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:149
msgid "TCP/UDP Port"
msgstr "TCP/UDP端口"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:150
msgid ""
"TCP/UDP protocol port number: 11010 means TCP/UDP will listen on port 11010."
"<br>If this is the Web configuration in the config file, please fill in the "
"same listening port for firewall allowance."
msgstr "tcp/udp协议，端口号：11010，表示 tcp/udp 将在 11010 上监听 <br>如果是配置文件Web配置请填写一样的监听端口用于防火墙放行"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:95
msgid ""
"The IPv4 address of this VPN node. If left empty, this node will only "
"forward packets and will not create a TUN device. (-i parameter)"
msgstr "此VPN节点的IPv4地址，如果为空，则此节点将仅转发数据包，不会创建TUN设备（-i 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:44
msgid ""
"The configuration file is located at /etc/easytier/config.toml<br>The "
"command-line startup parameters and the parameters in this configuration "
"file are not synchronized<br>Make sure to specify the TUN interface name and "
"port to enable automatic firewall allowance"
msgstr ""
"配置文件在/etc/easytier/config.toml<br>命令行的启动参数和此配置文件的参数不会同步<br>"
"注意填写tun网卡名和端口用以自动防火墙放行"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:227
msgid ""
"The default protocol used when connecting to peer nodes (--default-protocol "
"parameter)"
msgstr "连接对等节点时使用的默认协议（--default-protocol 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:188
msgid "The hostname used to identify this device (--hostname parameter)"
msgstr "用于标识此设备的主机名 （--hostname 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:73
msgid ""
"The network name used to identify this VPN network (--network-name parameter)"
msgstr "用于识别此 VPN 网络的网络名称（--network-name 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:368
#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:733
msgid "Trace"
msgstr "跟踪"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:699
msgid "UDP"
msgstr "UDP"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:734
msgid ""
"The URL of the API server, used for connecting the web frontend. (--api-host "
"parameter)<br>Example: http://[current device IP or resolved domain name]:"
"[API port]"
msgstr "API 服务器的 URL，用于 web 前端连接。（ --api-host 参数）<br>填写示例 http://[当前设备IP或解析的域名]:[API端口]"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:194
msgid "UUID"
msgstr "UUID"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:195
msgid ""
"Unique identifier used to recognize this device when connecting to the web "
"console, for issuing configuration files"
msgstr "连接web控制台时识别此设备的唯一标识，用于下发配置文件"

#: applications/luci-app-easytier/luasrc/view/easytier/other_upload.htm:4
msgid "Upload"
msgstr "上传"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:20
msgid "Upload Program"
msgstr "上传程序"

#: applications/luci-app-easytier/luasrc/view/easytier/other_upload.htm:2
msgid "Upload Program:"
msgstr "上传程序："

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:276
msgid "Use Userspace TCP/IP Stack"
msgstr "使用用户态TCP/IP协议栈"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:110
msgid ""
"Use a public shared node to discover peer nodes, same function as the "
"parameter above (-e parameter)"
msgstr "使用公共共享节点来发现对等节点，和上方参数作用一样 （-e 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:285
msgid ""
"Use system kernel to forward subnet proxy packets, disabling built-in NAT (--"
"proxy-forward-by-system parameter)"
msgstr "通过系统内核转发子网代理数据包，禁用内置NAT（ --proxy-forward-by-system 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:207
msgid "Used to identify the VPN node instance on the same machine. (-m parameter)"
msgstr "用于在同一台机器中标识此 VPN 节点的实例名称（-m 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:213
msgid "VPN Portal URL"
msgstr "VPN门户URL"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:526
msgid "VPN-Portal Info"
msgstr "VPN-Portal信息"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:235
msgid "Virtual Network Interface Name"
msgstr "虚拟网卡名称"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:173
msgid "WG Port"
msgstr "WireGuard端口"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:157
msgid "WS Port"
msgstr "WS端口"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:158
msgid ""
"WS protocol port number: 11011 means WS will listen on port 11011.<br>If "
"this is the Web configuration in the config file, please fill in the same "
"listening port for firewall allowance."
msgstr "ws协议，端口号：11011，表示 ws 将在 11011 上监听 <br>如果是配置文件Web配置请填写一样的监听端口用于防火墙放行"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:165
msgid "WSS Port"
msgstr "WSS端口"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:166
msgid ""
"WSS protocol port number: 11012 means WSS will listen on port 11012.<br>If "
"this is the Web configuration in the config file, please fill in the same "
"listening port for firewall allowance."
msgstr "wss协议，端口号：11012，表示 wss 将在 11012 上监听 <br>如果是配置文件Web配置请填写一样的监听端口用于防火墙放行"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:365
#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:730
msgid "Warning"
msgstr "警告"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:41
msgid "Web Configuration"
msgstr "Web配置"

#: applications/luci-app-easytier/luasrc/view/easytier/easytier_status.htm:25
msgid "Web Console"
msgstr "Web 控制台"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:726
msgid "Web Interface Port"
msgstr "web界面端口"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:64
msgid "Web Server Address"
msgstr "Web服务器地址"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:65
msgid ""
"Web configuration server address. (-w parameter)<br>For a self-hosted Web "
"server, use the format: udp://server_address:22020/username<br>For the "
"official Web server, use the format: username<br>Official Web Console: <a "
"href='https://easytier.cn/web'>easytier.cn/web</a>"
msgstr ""
"Web配置服务器地址。（-w 参数）<br>自建Web服务器 输入格式：udp://服务器地址:22020/账户名<br>官方Web服务器 输入格式：账户名 <br>"
"官方Web控制台：<a href='https://easytier.cn/web' target='_blank'>https://easytier.cn/web</a>"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:365
msgid ""
"When enabled, nodes with a different network name and password are not "
"allowed to handshake or relay via this node. (--private-mode parameter)"
msgstr "启用后则不允许使用了与本网络不相同的网络名称和密码的节点通过本节点进行握手或中转。（--private-mode 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:298
msgid "Whitelisted Networks"
msgstr "白名单网络"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:174
msgid ""
"WireGuard protocol port number: 11011 means WG will listen on port 11011."
"<br>If this is the Web configuration in the config file, please fill in the "
"same listening port for firewall allowance."
msgstr "WireGuard协议，端口号：11011，表示 wg 将在 11011 上监听 <br>如果是配置文件Web配置请填写一样的监听端口用于防火墙放行"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:360
msgid ""
"With Magic DNS, you can access other nodes using domain names, e.g., "
"<hostname>.et.net. Magic DNS will modify your system DNS settings, please "
"enable with caution. (--accept-dns parameter)"
msgstr "使用魔法DNS，您可以使用域名访问其他节点，例如：<hostname>.et.net。魔法DNS将修改您的系统DNS设置，请谨慎启用。（--accept-dns 参数）"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:607
msgid ""
"You can directly upload the binary programs easytier-core and easytier-cli, "
"or a compressed .zip archive. Uploading a new version will automatically "
"overwrite the old one. Download link: <a href='https://github.com/EasyTier/"
"EasyTier/releases' target='_blank'>github.com/EasyTier/EasyTier</a><br>The "
"uploaded files will be saved in the /tmp folder. If a custom program path is "
"specified, the program will be automatically moved to that path when started."
"<br>"
msgstr ""
"可直接上传二进制程序easytier-core和easytier-cli或者以.zip结尾的压缩包,上传新版本会自动覆盖旧版本，下载地址："
"<a href='https://github.com/EasyTier/EasyTier/releases' target='_blank'>github.com/EasyTier/EasyTier</a><br>"
"上传的文件将会保存在/tmp文件夹里，如果自定义了程序路径那么启动程序时将会自动移至自定义的路径<br>"

#: applications/luci-app-easytier/luasrc/controller/easytier.lua:11
msgid "core log"
msgstr "核心日志"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:228
#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:263
msgid "default"
msgstr "默认"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:591
msgid "easytier-core Binary Path"
msgstr "easytier-core程序路径"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:597
msgid "easytier-web Binary Path"
msgstr "easytier-web程序路径"

#: applications/luci-app-easytier/luasrc/controller/easytier.lua:14
msgid "web log"
msgstr "web日志"

#: applications/luci-app-easytier/luasrc/model/cbi/easytier.lua:264
msgid "zstd"
msgstr "zstd"

msgid "GEOIP_DB Path"
msgstr "GEOIP_DB IP数据库路径"

msgid ""
"GeoIP2 database file path used to locate the client. Defaults to an embedded file (country-level information only)."
"<br>Recommended: https://github.com/P3TERX/GeoLite.mmdb (--geoip-db parameter)"
msgstr ""
"数据库文件路径，用于查找客户端的位置，默认为嵌入文件（仅国家信息），"
"<br>推荐 https://github.com/P3TERX/GeoLite.mmdb (--geoip-db 参数)"

msgid "Interface IPV6 Address"
msgstr "接口IPV6地址"

msgid ""
"ipv6 address of this vpn node, can be used together with ipv4 for dual-stack operation"
"(--ipv6 parameter)"
msgstr ""
"虚拟网卡的IPv6地址，可与IPv4一起使用以进行双栈操作，"
"(--ipv6 参数)"

msgid "RPC Access Whitelist"
msgstr "RPC门户白名单"

msgid "rpc portal whitelist, only allow these addresses to access rpc portal (--rpc-portal-whitelist parameter)"
msgstr "RPC门户白名单，仅允许这些地址访问RPC门户，例如：127.0.0.1/32,*********/8,::1/128 (--rpc-portal-whitelist 参数)"

msgid "QUIC Port"
msgstr "QUIC 端口"

msgid "If this is the Web configuration in the config file, please fill in the same listening port for firewall allowance."
msgstr "如果是配置文件Web配置请填写一样的监听端口用于防火墙放行"

msgid "Number of Threads"
msgstr "线程数"

msgid "the number of threads to use, default is 2, only effective when multi-thread is enabled, must be greater than 2 (--multi-thread-count parameter)"
msgstr "使用的线程数，默认为2，仅在多线程模式下有效。取值必须大于2 (--multi-thread-count 参数)"

msgid "Forwarding Rate"
msgstr "转发速率"

msgid ""
"the maximum bps limit for foreign network relay, default is no limit. unit: BPS (bytes per second). "
"(--foreign-relay-bps-limit parameter)"
msgstr ""
"作为共享节点时，限制非本地网络的流量转发速率，默认无限制，单位 BPS （字节每秒）"
"(--foreign-relay-bps-limit 参数)"

msgid "Extra Parameters"
msgstr "额外参数"

msgid "Additional command-line arguments passed to the backend process"
msgstr "传递给后端进程的额外命令行参数"

msgid "ACL rules"
msgstr "ACL 规则"

msgid "Click the button to refresh and view ACL rules information"
msgstr "点击按钮刷新，查看 ACL 规则统计信息"

msgid "Mapped listener"
msgstr "Mapped listener信息"

msgid "Click the button to refresh and view manage mapped listeners"
msgstr "点击按钮刷新，查看管理映射的监听器"


