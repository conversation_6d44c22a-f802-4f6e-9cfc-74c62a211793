<%
	local uci = require 'luci.model.uci'.cursor()
	protocol="http://"
%>
<script type="text/javascript">//<![CDATA[
XHR.poll(3, '<%=url([[admin]], [[vpn]], [[easytier]], [[status]])%>', null,
    function(x, data) {
        var st = document.getElementById('easytier_status');
        if (data && st) {
            var clientStatus = data.crunning ? "<span style='color:green;'> 运行中</span><img src='https://www.right.com.cn/forum/data/attachment/album/202401/30/081238k459q2d5klacs8rk.gif' width='30px' alt=''>" : "<span style='color:red;'> 未运行</span>";
            var etstaContent = data.crunning ? "已运行：" + "<span style='color:#DA70D6;'>" + data.etsta + "</span>" : "";      
            var etcpuContent = data.crunning ? "<br>" + "&nbsp;&nbsp;CPU占用：" + "<span style='color:#6A5ACD;'>" + data.etcpu + "</span>" : "";  
            var etramContent = data.crunning ? "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + "&nbsp;&nbsp;&nbsp;&nbsp;内存占用：" + "<span style='color:#0000CD;'>" + data.etram + "</span>" : "";
            var ettagContent = data.crunning ? "<br>" + "&nbsp;&nbsp;当前版本：" + "<span style='color:#CD853F;'>" + data.ettag + "</span>" : ""; 
            var etnewtagContent = data.crunning ? "&nbsp;&nbsp;&nbsp;&nbsp;" + "&nbsp;&nbsp;&nbsp;最新版本：" + "<span style='color:#FFA500;'>" + data.etnewtag + "</span>" : ""; 

            var webStatus = data.wrunning ? "<span style='color:green;'> 运行中</span><img src='https://www.right.com.cn/forum/data/attachment/album/202401/30/081238k459q2d5klacs8rk.gif' width='30px' alt=''>" : "<span style='color:red;'> 未运行</span>";
            var etwebstaContent = data.wrunning ? "已运行：" + "<span style='color:#DA70D6;'>" + data.etwebsta + "</span>" : "";
            var etwebcpuContent = data.wrunning ? "<br>" + "&nbsp;&nbsp;CPU占用：" + "<span style='color:#6A5ACD;'>" + data.etwebcpu + "</span>" : "";  
            var etwebramContent = data.wrunning ? "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + "&nbsp;&nbsp;&nbsp;&nbsp;内存占用：" + "<span style='color:#0000CD;'>" + data.etwebram + "</span>" : "";
            var etwtagContent = data.wrunning ? "<br>" + "&nbsp;&nbsp;当前版本：" + "<span style='color:#CD853F;'>" + data.ettag + "</span>" : ""; 
            var etwnewtagContent = data.wrunning ? "&nbsp;&nbsp;&nbsp;&nbsp;" + "&nbsp;&nbsp;&nbsp;最新版本：" + "<span style='color:#FFA500;'>" + data.etnewtag + "</span>" : ""; 
            var etwebContent = "";
            if (data.port !== 0 && data.wrunning) {
            	etwebContent = "&nbsp;&nbsp;&nbsp;" + "<input class=\"cbi-button cbi-button-reload mar-10\" type=\"button\" value=\" <%:Web Console%> \" onclick=\"window.open('<%=protocol%>" + window.location.hostname + ":" + data.port + "')\"/>";
            }
            st.innerHTML = "<em><b>easytier-core " + clientStatus + etstaContent + etcpuContent + etramContent + ettagContent + etnewtagContent + "</b></em><br><br><em><b>easytier-web " + webStatus + etwebstaContent + etwebcpuContent + etwebramContent + etwtagContent + etwnewtagContent + etwebContent + "</b></em>";         
        }
    }
);
//]]>
</script>
<fieldset class="cbi-section">
    <p id="easytier_status">
        <em><%:Collecting data...%></em>
    </p>
</fieldset>
